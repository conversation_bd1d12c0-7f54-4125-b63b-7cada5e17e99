{"16": {"retrieval": {"name": "知识库", "description": "请前往开放平台的知识库上传文档，然后使用知识库功能进行检索。", "params": {"knowledge_id": {"name": "知识库ID", "description": "当涉及到知识库ID时，请前往开放平台的知识库模块进行创建或获取(是知识库ID不是文档ID！)", "type": "string", "required": true}, "prompt_template": {"name": "知识库模板", "description": "请求模型时的知识库模板, 请查看文档填写，否则不用填写", "type": "string", "required": false}}}, "web_search": {"name": "网页搜索", "description": "使用网页搜索功能，对用户输入的内容进行搜索", "params": {"enable": {"name": "启用", "description": "是否启用网页搜索", "type": "bool", "required": true}}}, "web_browser": {"name": "搜索工具", "description": "使用搜索工具功能，仅glm-4-alltools有效", "params": {"enable": {"name": "启用", "description": "是否启用搜索工具", "type": "bool", "required": true}}}, "drawing_tool": {"name": "绘图工具", "description": "使用绘图工具功能，仅glm-4-alltools有效", "params": {"enable": {"name": "启用", "description": "是否启用绘图工具", "type": "bool", "required": true}}}, "code_interpreter": {"name": "代码工具 ", "description": "使用代码工具 功能，仅glm-4-alltools有效", "params": {"sandbox": {"name": "沙盒模式", "description": "auto 自动调用沙盒环境执行代码，none 不启用沙盒环境，必填", "type": "string", "required": false}}}}, "15": {"use_openai_api": {"name": "使用OpenAI API", "description": "使用OpenAI API", "params": {"enable": {"name": "启用", "description": "是否启用使用OpenAI API, 开启用直接使用Baidu官方兼容OpenAI的API，不再做类型转换, 且上述插件无效", "type": "bool", "required": true}}}}, "17": {"web_search": {"name": "网页搜索", "description": "使用网页搜索功能，对用户输入的内容进行搜索", "params": {"enable": {"name": "启用", "description": "是否启用网页搜索", "type": "bool", "required": true}}}, "use_openai_api": {"name": "使用OpenAI API", "description": "使用OpenAI API", "params": {"enable": {"name": "启用", "description": "是否启用使用OpenAI API, 开启用直接使用ali官方兼容OpenAI的API，不再做类型转换, 且上述插件无效", "type": "bool", "required": true}}}}, "24": {"voice": {"name": "声音映射", "description": "将OpenAI的声音角色映射到azure的声音角色, 如果有role，请用|隔开，例如zh-CN-YunxiNeural|boy", "params": {"alloy": {"name": "alloy 映射", "description": "默认 zh-CN-YunxiNeural", "type": "string", "required": true}, "echo": {"name": "echo 映射", "description": "默认 zh-CN-YunyangNeural", "type": "string", "required": true}, "fable": {"name": "fable 映射", "description": "默认 zh-CN-YunxiNeural|boy", "type": "string", "required": true}, "onyx": {"name": "onyx 映射", "description": "默认 zh-CN-YunyeNeural", "type": "string", "required": true}, "nova": {"name": "nova 映射", "description": "默认 zh-C<PERSON>-XiaochenNeural", "type": "string", "required": true}, "shimmer": {"name": "shimmer 映射", "description": "默认 zh-<PERSON><PERSON>-XiaohanNeural", "type": "string", "required": true}}}}, "39": {"headers": {"name": "Header 配置", "description": "本配置主要是用于使用cloudflare Zero Trust将端口暴露到公网时，需要配置的header", "params": {"CF-Access-Client-Id": {"name": "CF-Access-Client-Id", "description": "CF-Access-Client-Id", "type": "string", "required": true}, "CF-Access-Client-Secret": {"name": "CF-Access-Client-Secret", "description": "CF-Access-Client-Secret", "type": "string", "required": true}}}}, "25": {"code_execution": {"name": "代码执行", "description": "使用代码执行功能，开启后，计算tokens不准确，建议个人使用开启", "params": {"enable": {"name": "启用", "description": "是否启用代码执行", "type": "bool", "required": true}}}, "use_openai_api": {"name": "使用OpenAI API", "description": "使用OpenAI API", "params": {"enable": {"name": "启用", "description": "是否启用使用OpenAI API, 开启用直接使用gemini官方兼容OpenAI的API，不再做类型转换", "type": "bool", "required": true}}}}, "27": {"voice": {"name": "声音映射", "description": "将OpenAI的声音角色映射到minimax的声音角色, 如果有emotion，请用|隔开，例如female-chengshu|happy", "params": {"alloy": {"name": "alloy 映射", "description": "默认 female-chengshu", "type": "string", "required": true}, "echo": {"name": "echo 映射", "description": "默认 male-qn-qingse", "type": "string", "required": true}, "fable": {"name": "fable 映射", "description": "默认 male-qn-jingying", "type": "string", "required": true}, "onyx": {"name": "onyx 映射", "description": "默认 presenter_male", "type": "string", "required": true}, "nova": {"name": "nova 映射", "description": "默认 presenter_female", "type": "string", "required": true}, "shimmer": {"name": "shimmer 映射", "description": "默认 audiobook_female_1", "type": "string", "required": true}}}}, "8": {"customize": {"name": "自定义参数", "description": "你可以自定义每个请求的地址，空为默认值，disable为禁用该请求", "params": {"1": {"name": "ChatCompletions地址", "description": "默认为： /v1/chat/completions", "type": "string", "required": false}, "2": {"name": "Completion地址", "description": "默认为： /v1/completions", "type": "string", "required": false}, "3": {"name": "Embeddings地址", "description": "默认为： /v1/embeddings", "type": "string", "required": false}, "4": {"name": "moderations地址", "description": "默认为： /v1/moderations", "type": "string", "required": false}, "5": {"name": "ImagesGenerations地址", "description": "默认为： /v1/images/generations", "type": "string", "required": false}, "6": {"name": "ImagesEdit地址", "description": "默认为： /v1/images/edits", "type": "string", "required": false}, "7": {"name": "ImagesVariations地址", "description": "默认为： /v1/images/variations", "type": "string", "required": false}, "9": {"name": "AudioSpeech地址", "description": "默认为： /v1/audio/speech", "type": "string", "required": false}, "10": {"name": "AudioTranscriptions地址", "description": "默认为： /v1/audio/transcriptions", "type": "string", "required": false}, "11": {"name": "AudioTranslations地址", "description": "默认为： /v1/audio/translations", "type": "string", "required": false}}}}, "20": {"other": {"name": "覆盖参数", "description": "覆盖参数", "params": {"provider": {"name": "供应商", "description": "格式{\"anthropic\": { \"order\": [\"Anthropic\", \"Amazon Bedrock\"], \"ignore\": [\"Google\"], \"allow_fallbacks\": true }}", "type": "string", "required": true}}}}}