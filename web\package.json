{"name": "one_hub_web", "version": "1.2.0", "proxy": "http://127.0.0.1:3000", "private": true, "homepage": "", "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@iconify/react": "^5.0.2", "@lobehub/i18n-cli": "^1.20.3", "@mui/icons-material": "^5.15.7", "@mui/lab": "^5.0.0-alpha.163", "@mui/material": "^5.15.7", "@mui/system": "^5.15.7", "@mui/utils": "^5.15.7", "@mui/x-data-grid": "^6.19.4", "@mui/x-date-pickers": "^6.18.5", "@tabler/icons-react": "^2.46.0", "@vitejs/plugin-react": "^4.3.1", "apexcharts": "3.35.3", "axios": "^0.30.0", "country-flag-icons": "^1.5.19", "dayjs": "^1.11.10", "decimal.js": "^10.4.3", "formik": "^2.4.5", "framer-motion": "^11.0.3", "highlight.js": "^11.10.0", "history": "^5.3.0", "i18next": "^23.11.5", "i18next-browser-languagedetector": "^8.0.0", "marked": "^4.1.1", "material-ui-popup-state": "^5.0.10", "notistack": "^3.0.1", "prop-types": "^15.8.1", "react": "^18.2.0", "react-apexcharts": "1.4.0", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-i18next": "^14.1.2", "react-perfect-scrollbar": "^1.5.8", "react-qrcode-logo": "^3.0.0", "react-redux": "^9.1.0", "react-router": "6.21.3", "react-router-dom": "6.21.3", "react-turnstile": "^1.1.2", "redux": "^5.0.1", "vite": "^5.4.18", "vite-jsconfig-paths": "^2.0.1", "web-vitals": "^4.2.3", "yup": "^0.32.11"}, "scripts": {"dev": "vite", "start": "vite preview", "build": "vite build --outDir build", "i18n": "lobe-i18n", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\""}, "eslintConfig": {"extends": ["react-app"]}, "babel": {"presets": ["@babel/preset-react"]}, "browserslist": {"production": ["defaults", "not IE 11"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/eslint-parser": "^7.23.10", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "immutable": "^4.3.5", "prettier": "^3.2.4", "sass": "^1.70.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}