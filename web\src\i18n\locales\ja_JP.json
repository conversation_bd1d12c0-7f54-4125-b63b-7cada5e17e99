{"System Logs": "システムログ", "view_more_logs_on_server": "より多くのシステムログを表示するには、サーバーにアクセスしてください", "systemInfo": "システム情報", "Auto Refresh": "自動リフレッシュ", "Refresh Interval": "リフレッシュ間隔", "1 second": "1秒", "3 seconds": "3秒", "5 seconds": "5秒", "10 seconds": "10秒", "30 seconds": "30秒", "1 minute": "1分", "Max Entries": "最大エントリー数", "Clear": "クリア", "No logs available": "ログはありません", "No matching logs found": "一致するログが見つかりません", "Search logs...": "ログを検索...", "invite_count": "招待人数", "invite_reward": "招待報酬", "AZURE_OPENAI_ENDPOINT": "AZURE_OPENAI_ENDPOINT", "CheckUpdatesTable": {"addNewOnly": "新規追加のみ", "cancel": "キャンセル", "checkUpdates": "更新を確認", "dataFormatIncorrect": "データ形式が正しくありません", "fetchData": "データを取得", "inputMultiplierChanged": "入力倍率が", "newModels": "新しいモデル", "noNewModels": "新しいモデルはありません", "noUpdates": "更新なし", "note": "注意", "operationCompleted": "操作が正常に完了しました！", "outputMultiplierChanged": "出力倍率が", "overwriteData": "データを上書き", "overwriteOrAddOnly": "上書きまたは新規追加のみを選択できます。上書きを選択すると、追加したモデルの価格が削除され、リモート設定が完全に使用されます。新規追加のみを選択すると、新しいモデルの価格のみが追加されます。", "pleaseFetchData": "まずデータを取得してください", "priceChangeModels": "価格変更モデル（参考用。自分で価格を変更した場合は無視してください）", "to": "から", "url": "URL", "priceServerTotal": "価格サーバー総数", "updateModeAdd": "新規追加のみ", "updateModeUpdate": "価格変更のみ", "updateModeOverwrite": "上書き", "added": "追加", "extraRatiosAdded": "拡張倍率の追加", "extraRatiosChanged": "拡大率の変化", "extraRatiosChanges": "拡大率の変動", "extraRatiosChangesTip": "拡張率変更：{{changes}}", "extraRatiosRemoved": "拡張倍率を削除します", "modified": "変更", "removed": "削除"}, "sidebar": {"totalQuota": "総額", "remainingBalance": "残高"}, "Header 配置": "ヘッダー構成", "about": {"aboutDescription": "設定ページで「About」内容を設定できます。HTML & Markdownをサポートしています", "aboutTitle": "について", "loadingError": "「About」の内容の読み込みに失敗しました...", "projectRepo": "プロジェクトリポジトリ："}, "alloy 映射": "合金マッピング", "analytics": "分析", "analytics_index": {"active": "アクティブ", "averageLatency": "平均遅延", "channelCount": "チャネル数", "consumptionStatistics": "消費統計", "directRegistration": "直接登録", "disabled": "無効", "endTime": "終了時間", "invitationRegistration": "招待登録", "redeemCodeIssued": "発行された引き換えコード", "redemptionStatistics": "交換統計", "registrationStatistics": "登録統計", "requestsCount": "リクエスト数", "startTime": "開始時間", "testDisabled": "テスト無効", "tokensStatistics": "トークン統計", "totalUserBalance": "ユーザーの総残高", "totalUserSpending": "ユーザーの総消費額", "totalUsers": "ユーザーの総数", "unused": "未使用", "used": "使用済み", "rechargeStatistics": "チャージ統計", "redemptionCode": "引き換えコード", "order": "注文", "realTimeTraffic": "リアルタイム流量", "tpmDescription": "TPM (Token/分)", "last60SecondsStats": "最近60秒の統計"}, "auth": {"invalidLink": "無効なリンク", "newPassword": "新しいパスワード", "newPasswordEdit": "ログイン後は速やかにパスワードを変更してください", "newPasswordInfo": "新しいパスワードは次のとおりです。", "restPassword": "パスワードを再設定する", "restPasswordClick": "クリックしてパスワードをリセット"}, "channel": "チャネル", "channel_edit": {"addModelHeader": "カスタムヘッダーを追加", "addModelMapping": "モデルマッピングの追加", "addModelMappingByJson": "モデルマッピング（JSON）の追加", "addSuccess": "無事作成されました！", "batchAdd": "バッチで追加", "batchBaseurlTip": "1 行に 1 つずつ、順序は次のキーに対応します。対応するキーが一致しない場合は、最初のキーがデフォルトで使用されます。", "batchKeytip": "、1 行に 1 つのキー", "collapse": "折りたたむ", "customModelTip": "カスタマイズ: をクリックするか Enter キーを押して入力します。", "editSuccess": "アップデート完了！", "expand": "展開する", "inputAllModel": "すべてのモデルを入力してください", "inputChannelModel": "チャネルサポートモデルを入力します", "invalidJson": "無効なJSON", "isEnable": "有効にするかどうか", "jsonInputLabel": "JSONオブジェクト、キーはリクエストモデルであり、値は実際の転送モデルです。", "modelHeaderKey": "カスタムヘッダーキー", "modelHeaderValue": "カスタムヘッダー値", "modelListError": "モデルリストの取得に失敗しました", "modelMappingKey": "ユーザーリクエストモデル", "modelMappingValue": "実際の転送モデル", "requiredBaseUrl": "チャネル API アドレスを空にすることはできません", "requiredChannel": "チャンネルを空にすることはできません", "requiredGroup": "ユーザーグループを空にすることはできません", "requiredKey": "キーを空にすることはできません", "requiredModels": "モデルを空にすることはできません", "requiredName": "名前は必須です", "validJson": "有効な JSON 文字列である必要があります", "copyModels": "モデルをコピーします", "mapAdd": "{{name}}を追加", "mapAddByJson": "JSONを使用して{{name}}を追加します。", "mapJsonInput": "JSON 入力", "listJsonError": "JSON形式が間違っています。入力が配列形式であることを確認してください。", "listJsonHelperText": "JSON配列形式を入力してください。例：[\"プロジェクト1\", \"プロジェクト2\", \"プロジェクト3\"]", "modelsFetched": "モデルの取得に成功しました", "selectedMappingCount": "選択されたモデルマッピング{{count}}個", "addMapping": "モデルマッピングを追加", "addToModelMapping": "モデルマッピングに追加", "modelMapping": "モデルマッピング", "modelMappingSettings": "モデルマッピング設定", "prefixOrSuffix": "プレフィックス／サフィックス", "removePrefixHelp": "削除する前のプレフィックスを入力してください", "removeSuffixHelp": "削除する後のサフィックスを入力してください", "addPlusSign": "マッピング前の課金を使用", "mappingPreview": "マッピングプレビュー", "removePrefix": "前缀を削除", "removeSuffix": "後缀を削除", "collapseList": "リストを折りたたむ", "expandList": "リストを展開"}, "channel_index": {"AzureApiVersion": "Azureのバージョン番号", "actions": "操作", "all": "すべて", "batchAzureAPISuccess": "{{count}} 個のデータが正常に更新されました", "batchDelete": "モデルを一括で削除する", "batchDeleteModel": "完全なモデル名を入力してください", "batchDeleteSuccess": "{{count}} 個のデータが正常に削除されました", "batchDeleteTip": "チャンネルにモデルが 1 つしかない場合は、リストから手動でチャンネルを削除してください。", "batchProcessing": "一括処理", "channel": "チャネル", "channelList": "チャネルリスト", "channelName": "チャネル名", "channelTags": "チャネルタグ", "channelType": "チャネルタイプ", "deleteDisabledChannels": "無効なチャネルを削除", "description1": "1. 優先度が高いほど優先的に使用されます。(同じ優先度のノードがすべて凍結または無効になっている場合のみ、低い優先度のノードが使用されます)", "description2": "2. 同じ優先度の場合：ウェイトによる負荷分散が行われます(ウェイト付きランダム)", "description3": "3. 「再試行回数」と「再試行間隔」が一般設定で設定されている場合、失敗後に再試行が行われます。", "description4": "4. 再試行ロジック：1）優先度の高いノードで再試行を行います。優先度が高いノードがすべて凍結されている場合のみ、優先度の低いノードで再試行が行われます。2）「再試行間隔」が設定されている場合、特定のチャネルで失敗した場合、一定時間凍結され、そのチャネルはもう一度使用されません。凍結時間が終了するまで。3）再試行回数が完了すると、直ちに終了します。", "disabled": "無効", "enabled": "有効", "filterTags": "フィルタータグ", "group": "グループ", "inputAPIVersion": "APIのバージョン番号を入力してください", "model": "モデル", "modelName": "モデル名", "name": "名称", "newChannel": "新規チャネル", "otherParameters": "その他のパラメータ", "priority": "優先度", "priorityWeightExplanation": "優先度/ウェイトの説明：", "refreshClearSearchConditions": "検索条件をリフレッシュ/クリア", "replaceValue": "交換価値", "responseTime": "応答時間", "search": "検索", "selectAll": "すべて選択", "showAll": "すべて表示", "speedTestDisabled": "速度テスト無効", "status": "ステータス", "supplier": "サプライヤー", "tags": "タグ", "testAllChannels": "すべてのチャネルをテスト", "testModel": "テストモデル", "type": "タイプ", "unselectAll": "すべてを非選択にする", "updateEnabledBalance": "有効残高を更新", "usedBalance": "使用済み/残高", "weight": "ウェイト", "onlyTags": "タグのみ表示"}, "channel_row": {"auto": "自動", "canModels": "利用可能なモデル:", "channelWeb": "公式ウェブサイト", "clickUpdateQuota": "クリックして残高を更新します", "delChannel": "チャンネルの削除", "delChannelCount": "合計 {{count}} 個の無効なチャンネルをすべて削除しました", "delChannelInfo": "チャンネルを削除するかどうか", "delTag": "タグの削除", "delTagInfo1": "ラベルを削除するかどうか", "delTagInfo2": "⚠️ 注: この操作によりチャンネルが削除されます。", "manual": "マニュアル", "modelTestSuccess": "チャンネル {{channel}}: {{model}} のテストは成功し、所要時間は {{time}} 秒でした。", "modelTestTip": "最初にテストモデルをセットアップしてください", "onlyChat": "チャットモデルのみをサポート", "otherArg": "その他のパラメータ:", "priorityTip": "優先度を 0 未満にすることはできません", "proxy": "エージェントのアドレス:", "test": "テスト", "testAllChannel": "すべてのチャネルのテストが正常に開始されました。結果を表示するにはページを更新してください。", "testModels": "スピードテストモデル：", "updateChannelBalance": "有効になっているすべてのチャンネルバランスが更新されました。", "updateOk": "アップデート完了！", "weightTip": "重みは 1 未満にはできません", "check": "検出", "batchAddIDRequired": "少なくとも1つのチャネルを選択してください", "batchDelete": "一括削除", "batchDeleteConfirm": "選択された {{count}} 個のチャネルを削除しますか？この操作は取り消せません。", "batchDeleteError": "一括削除に失敗しました：{{message}}", "batchDeleteErrorTip": "一括削除エラー：{{message}}", "batchDeleteSuccess": "一括削除しました！", "batchDeleteTip": "選択したチャネルを一括削除しますか？", "deleteTag": "タグを削除します。", "deleteTagAndChannels": "タグとそのすべてのチャネルを削除します。", "deleteTagConfirm": "タグ {{tag}} およびそのすべてのチャネルを削除してもよろしいですか？この操作は取り消せません。", "deleteTagError": "タグの削除に失敗しました：{{message}}", "deleteTagSuccess": "タグ {{tag}} が削除されました", "disable": "無効", "disableAllChannels": "すべてのチャネルを無効にする", "disableTagChannels": "タグチャネルを無効にする", "enable": "有効", "enableAllChannels": "すべてのチャネルを有効にする", "enableTagChannels": "タグチャネルを有効にする", "getTagChannelsError": "タグチャネルの取得に失敗しました：{{message}}", "getTagChannelsErrorTip": "タグチャネルの取得中にエラーが発生しました：{{message}}", "noTagChannels": "タグチャネルが見つかりません", "priorityUpdateError": "優先度の更新に失敗しました：{{message}}", "priorityUpdateSuccess": "優先度が更新されました", "refreshList": "リストを更新する", "tag": "タグ", "tagChannelList": "タグチャネルリスト", "tagChannelsConfirm": "すべてのチャネルで{{action}}タグ{{tag}}を確認しますか？", "tagChannelsError": "アクション{{action}}のラベルチャネルが失敗しました: {{message}}", "tagChannelsSuccess": "タグチャネル{{action}}が成功しました", "weightUpdateError": "重みの更新に失敗しました：{{message}}", "weightUpdateSuccess": "重みが更新されました", "key": "鍵", "keyRequired": "キーを入力してください。"}, "common": {"again": "再試行 ({{count}})", "back": "戻る", "bindOk": "バインド成功！", "cancel": "キャンセル", "close": "閉鎖", "copyUrl": "アドレスをコピーする", "create": "作成する", "delete": "消去", "deleteConfirm": "{{title}}を削除しますか？", "disable": "無効", "downImg": "写真をダウンロードする", "edit": "編集", "enable": "アクティブ化された", "execute": "{{title}}を実行しますか？", "executeConfirm": "実行", "exhaust": "疲れ果てた", "expired": "期限切れ", "imgUrl": "地図の住所", "link": "リンク", "loginOk": "ログイン成功！", "newWindos": "新しいウィンドウで開く", "noData": "データなし", "none": "なし", "processing": "処理...", "registerOk": "登録完了！", "registerTip": "確認コードは正常に送信されました。メールを確認してください。", "saveSuccess": "正常に保存！", "serverError": "サーバーエラー", "show": "見せる", "submit": "提出する", "unableServer": "サーバーに正しく接続できません!", "unableServerTip": "新しいバージョンが利用可能です: {{version}}、ショートカット キー Shift F5 を使用してページを更新してください", "unknown": "未知", "verificationCode": "検証コード", "deleteError": "削除に失敗しました：{{message}}", "deleteSuccess": "削除しました！", "jsonFormatError": "JSON形式が間違っています", "actions": "操作 -> 操作", "noDataAvailable": "データがありません。", "pageSize": "ページごとのアイテム数", "search": "検索", "total": "総数"}, "dashboard": "ダッシュボード", "dashboard_index": {"balance": "残高", "calls": "コール回数", "model_name": "モデル名", "model_price": "現在のモデル価格", "no_data": "データなし", "no_data_available": "利用可能データなし", "other_models": "他のモデル", "statistics": "統計", "today_consumption": "今日の消費", "today_requests": "今日のリクエスト", "today_tokens": "今日のトークン", "unknown": "不明", "used": "使用済み", "week_model_statistics": "近七日模型統計", "week_consumption_log": "近七日消費日志", "7days_model_usage_pie": "近七日模型使用情況", "date": "日付", "request_count": "リクエスト数", "amount": "金額", "tokens": "<PERSON><PERSON><PERSON>(入力/出力)", "cache_tokens": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "request_time": "リクエスト時間(s)", "loading": "読み込み中...", "quickStart": "クイックスタート", "quickStartTip": "下のボタンをクリックすると、システムはsys_playgroundのトークンを自動的に作成します", "ComparedWithYesterday": "昨日と比較", "usage": "使用率", "RPM": "現在のRPM", "TPM": "現在のTPM", "total": "総回数", "availability": "稼働率", "tab_status": "ステータス", "tab_dashboard": "ダッシュボード", "title": "ダッシュボード"}, "description": "All in one の OpenAI インターフェース\nさまざまな API アクセス方法を統合\nワンクリックで展開、即座に使用可能", "echo 映射": "エコーマッピング", "error": {"unknownError": "未知の間違い"}, "fable 映射": "寓話のマッピング", "footer": {"basedOn": "ベース", "developedBy": "開発者", "license": "MIT ライセンスに基づく", "sourceCode": "ソースコードは"}, "home": {"loadingErr": "ホームページのコンテンツを読み込めませんでした..."}, "inviteCard": {"copyButton": {"copy": "コピー", "generate": "生成"}, "generateInvite": "クリックして招待リンクを生成する", "inviteDescription": "招待リンクを共有して、友達を招待して登録すると、リワードを獲得できます！", "inviteReward": "招待リワード", "inviteUrlLabel": "招待リンク"}, "jump": "リダイレクト中...", "log": "ログ", "invoice": "月次請求書", "midjourney": "Midjourney", "invoice_index": {"invoice": "月次請求書", "refresh": "更新", "id": "ID", "name": "名前", "status": "ステータス", "amount": "金額", "createdTime": "作成時間", "paidTime": "支払時間", "actions": "アクション", "deleteInvoice": "請求書を削除", "confirmDeleteInvoice": "この請求書を削除してもよろしいですか", "close": "閉じる", "delete": "削除", "paid": "支払済み", "unpaid": "未払い", "date": "請求時間", "requestCount": "リクエスト総数", "quota": "消費金額", "promptTokens": "入力トークン", "completionTokens": "出力トークン", "requestTime": "リクエスト時間", "tokens": "トークン（入力/出力）", "alert": "毎月1日の深夜に前月の請求データが生成されます", "viewInvoice": "請求書を表示", "modelName": "モデル名", "option": "オプション", "username": "ユーザー名", "email": "メール", "userinfo": "ユーザー情報", "usage_statistics": "使用統計", "download": "PDFをダウンロード", "usage_details": "請求書の詳細", "summary": "概要"}, "logPage": {"cachedTokens": "<PERSON><PERSON> (* {{ ratio }})", "channelLabel": "チャネル", "columnSettings": "列設定", "selectColumns": "列を選択", "columnSelectAll": "すべて選択", "detailLabel": "詳細", "durationLabel": "時間", "durationTooltip": "t/s: 出力トークンの数を総生成時間で割った値であり、生成速度を示します。", "groupLabel": "グループ", "inputAudioTokens": "Input audio tokens (* {{ ratio }})", "inputLabel": "入力", "inputTextTokens": "Input text tokens (* {{ ratio }})", "modelLabel": "モデル", "outputAudioTokens": "Output audio tokens (* {{ ratio }})", "outputLabel": "出力", "outputTextTokens": "Output text tokens (* {{ ratio }})", "quotaLabel": "クォータ", "refreshButton": "更新/検索条件をクリア", "searchButton": "検索", "searchLogsInfo": "チャージ記録および招待記録はログで確認してください。チャージ記録はログでタイプ【チャージ】を選択して確認してください；招待記録はログで【システム】を選択して確認してください", "timeLabel": "時間", "title": "ログ", "tokenLabel": "トークン", "totalInputTokens": "Total input tokens", "totalOutputTokens": "Total output tokens", "typeLabel": "タイプ", "userLabel": "ユーザー", "sourceIp": "Source IP", "logType": {"all": "すべて", "recharge": "チャージ", "consumption": "消費", "management": "管理", "system": "システム"}, "content": {"calculate_steps": "計算手順:", "calculate_steps_tip": "PS：このシステムはポイントに基づいて計算され、すべての金額はポイント換算であり、1ポイント＝$0.000002です。最低消費額は1ポイントであり、この計算手順は参考用として提供されます。実際の料金が優先されます。", "channel_group": "グループ：{{ channel_group }}", "group_discount": "グループ割引：{{ discount }}", "input_price": "入力：${{ price }} /M", "original_input_price": "元の入力価格：${{ price }}/M", "original_output_price": "元の出力価格：${{ price }}/M", "original_times_price": "元の価格：${{ times }} / 回", "output_price": "出力: ${{ price }} /M", "times_price": "${{ times }} / 回", "free": "無料", "old_log": "旧バージョンのログ", "illustrate": "* この記録はシステムアップグレード前のログであり"}, "quotaDetail": {"saved": "節約 ", "originalPrice": "元の価格", "inputPrice": "入力価格", "outputPrice": "出力価格", "groupRatio": "グループ倍率", "groupRatioValue": "グループ倍率", "actualPrice": "実際の価格", "input": "入力", "output": "出力", "finalCalculation": "最終計算", "originalBilling": "元の請求", "actualBilling": "実際の請求", "calculationNote": "PS：このシステムはポイントに基づいて計算され、すべての金額はポイント換算であり、1ポイント＝$0.000002です。最低消費額は1ポイントであり、この計算手順は参考用として提供されます。実際の料金が優先されます。", "times": "倍"}, "cachedReadTokens": "キャッシュからトークンを読み取ります（* {{ ratio }} )", "cachedWriteTokens": "トークンのキャッシュ書き込み（* {{ ratio }} ）", "reasoningTokens": "推理トークン"}, "login": {"codeRequired": "確認コードを入力する必要があります", "forgetPassword": "パスワードを忘れましたか？", "githubCountError": "エラーが発生しました。{{count}} を再試行しています...", "githubError": "操作が失敗し、ログイン インターフェイスにリダイレクトされました...", "githubLogin": "GitHub ログイン", "larkLogin": "フェイシュログイン", "oidcCountError": "{{count}} 回目のリトライ中にエラーが発生しました...", "oidcError": "操作失敗、ログインページにリダイレクト中...", "oidcLogin": "OIDC ログイン", "password": "パスワード", "passwordRequired": "パスワードは必須です", "passwordRest": "パスワードリセットの確認", "qrCode": "QRコード", "useGithubLogin": "Github を使用してログインする", "useLarkLogin": "フェイシュでログイン", "useOIDCLogin": "OIDC ログインを使用する", "useWechatLogin": "Wechatでログイン", "usernameOrEmail": "ユーザー名/メール", "usernameRequired": "ユーザー名/メールは必須です", "wechatLoginInfo": "WeChatでQRコードをスキャンして公式アカウントをフォローし、「認証コード」を入力して認証コードを取得してください（3分以内有効）", "wechatVerificationCodeLogin": "WeChat認証コードログイン"}, "menu": {"about": "概要", "console": "コンソール", "error": "エラー", "home": "ホーム", "login": "ログイン", "signout": "ログアウト", "signup": "サインアップ", "status": "ステータス", "unknownVersion": "未知バージョン", "welcomeBack": "おかえりなさい"}, "midjourneyPage": {"channel": "チャネル", "failureReason": "失敗理由", "midjourney": "Midjourney", "progress": "進捗", "prompt": "Prompt", "promptEn": "PromptEn", "refreshClearSearch": "リフレッシュ/検索条件をクリア", "resultImage": "結果画像", "search": "検索", "submissionResult": "提出結果", "submitTime": "提出時間", "taskID": "タスクID", "taskStatus": "タスクステータス", "timeConsuming": "時間がかかる", "type": "タイプ", "user": "ユーザー"}, "model_price": "モデル価格", "modelpricePage": {"availableModels": "利用可能なモデル", "channelType": "サプライヤー", "group": "グループ", "inputMultiplier": "価格を入力してください", "model": "モデル名", "noneGroup": "現在のグループは利用できません", "outputMultiplier": "価格を出力します。", "search": "検索", "times": "回数制課金", "tokens": "量に応じて支払う", "type": "タイプ", "input_audio_tokens": "オーディオ入力倍率", "other": "その他", "output_audio_tokens": "オーディオ出力倍率", "rate": "倍率", "RPM": "APIレート", "free": "無料", "cached_tokens": "キャッシュ倍率", "cached_write_tokens": "キャッシュ書き込み倍率", "cached_read_tokens": "キャッシュ読み取り倍率", "reasoning_tokens": "推論倍率", "input_text_tokens": "入力テキスト倍率", "output_text_tokens": "出力テキスト倍率", "all": "すべて", "extraRatios": "価格拡張", "input": "入力", "input_image_tokens": "画像倍率", "noExtraRatios": "拡張価格はありません", "output": "出力", "output_image_tokens": "画像倍率", "price": "価格", "showAll": "すべて表示", "onlyAvailable": "利用可能なだけ表示"}, "nova 映射": "新星マッピング", "onyx 映射": "オニキスマッピング", "operation": "オペレーション", "orderlogPage": {"endTimeLabel": "終了時間", "gatewayIdLabel": "ゲートウェイID", "gatewayNoLabel": "ゲートウェイ注文番号", "placeholder": {"gatewayId": "ゲートウェイID", "gatewayNo": "ゲートウェイ注文番号", "tradeNo": "注文番号", "userId": "ユーザーID"}, "refreshClear": "リフレッシュ/検索条件をクリア", "search": "検索", "startTimeLabel": "開始時間", "statusLabel": "ステータス", "statusOptions": {"status1": "ステータス1", "status2": "ステータス2", "status3": "ステータス3"}, "tableHeaders": {"amount": "リチャージ金額", "created_at": "時間", "discount": "割引額", "fee": "手数料", "gateway_id": "支払いゲートウェイ", "gateway_no": "ゲートウェイ注文番号", "order_amount": "実際の支払い額", "quota": "クレジットポイント", "status": "ステータス", "trade_no": "注文番号", "user_id": "ユーザー"}, "title": "ログ", "tradeNoLabel": "注文番号", "userIdLabel": "ユーザーID"}, "paySetting": "支払い設定", "payment": "支払い", "paymentGatewayPage": {"createPayment": "支払いを作成", "refreshClear": "リフレッシュ/検索条件をクリア", "search": "検索", "tableHeaders": {"action": "アクション", "createdAt": "作成日", "enable": "有効にする", "fixedFee": "固定手数料", "icon": "アイコン", "id": "ID", "name": "名前", "percentFee": "パーセント手数料", "sort": "並び替え", "type": "タイプ", "uuid": "UUID"}, "title": "支払いゲートウェイ"}, "paymentPage": {"gatewaySettings": "ゲートウェイ設定", "orderList": "注文リスト"}, "payment_edit": {"FixedTip": "支払いごとに米ドルで固定の手数料がかかります", "addOk": "無事作成されました！", "currencyTip": "このゲートウェイはどの通貨で請求されますか? 対応するゲートウェイのドキュメントを確認してください。", "currencyType": "ゲートウェイ通貨の種類", "notifyDomain": "コールバックドメイン名", "notifyDomainTip": "支払いコールバックのドメイン名。自分で設定していない限り、空のままにしておきます。", "paymentEdit": "支払いの編集", "paymentType": "支払いの種類", "percentTip": "各支払いにはパーセンテージに基づいて手数料がかかります。5% の場合は、0.05 を入力してください。", "requiredCurrency": "通貨を空にすることはできません", "requiredFixedFee": "固定手数料は 0 未満にはできません", "requiredIcon": "アイコンを空にすることはできません", "requiredPercentFee": "割合手数料は 0 未満にはできません", "updateOk": "アップデート完了！"}, "payment_row": {"delPayment": "支払いを削除する", "delPaymentTip": "支払いを削除するかどうか", "sortTip": "並べ替えを 0 未満にすることはできません"}, "playground": "遊び場", "pricing": "価格設定", "pricingPage": {"ModelCount": "モデル数", "currencyInfo1": "ドル", "currencyInfo2": "：1 === $0.002 / 1K tokens", "currencyInfo3": "人民元", "currencyInfo4": "： 1 === ￥0.014 / 1k tokens", "currencyInfo5": "例", "currencyInfo6": "：gpt-4 入力： $0.03 / 1K tokens 完了：$0.06 / 1K tokens", "currencyInfo7": "0.03 / 0.002 = 15, 0.06 / 0.002 = 30、入力倍率は 15、完了倍率は 30", "errPricesWarning": "サプライヤータイプが間違っているモデルがあります。設定してください：", "multipleOperation": "複数操作", "newButton": "新規作成", "noPriceModelWarning": "未設定の価格モデルがあります。価格を設定してください：", "refreshButton": "更新", "singleOperation": "シングル操作", "updatePricesButton": "価格を更新", "title": "モデル価格"}, "pricing_edit": {"channelType": "チャンネルの種類", "channelTypeErr": "チャンネルタイプエラー", "channelTypeErr2": "チャンネルタイプが間違っています", "delGroup": "価格グループの削除", "delGroupTip": "価格グループを削除しますか?", "delInfoTip": "本当に {{name}} を削除してもよろしいですか?", "delTip": "削除を確認しますか？", "inputVal": "入力倍率は 0 以上である必要があります", "model": "モデル", "modelNameRe": "モデル名を重複して使用することはできません", "modelTip": "価格でサポートされるモデルを選択してください。たとえば、gpt-3.5* のように、モデルに一致するワイルドカード文字 * を入力することもできます。これは、gpt-3.5 で始まるすべてのモデルがサポートされることを意味します。最後の桁に があり、その前に文字が必要です。例: gpt-3.5* は正しいですが、*gpt-3.5 は間違っています。", "name": "名前", "outputVal": "出力倍率は 0 以上でなければなりません", "requiredChannelType": "チャネルタイプを空にすることはできません", "requiredInput": "入力倍率を空にすることはできません", "requiredModelName": "モデル名を空にすることはできません", "requiredModels": "モデルを空にすることはできません", "requiredOutput": "出力倍率を空にすることはできません", "requiredType": "型を空にすることはできません", "saveOk": "正常に保存", "type": "タイプ", "typeCheck": "タイプはトークンまたは時間のみです", "typeErr": "タイプエラー", "locked_title": "価格をロックする", "locked": "ロックされた価格", "unlocked": "ロックされていない価格", "lockedTip": "価格をロックすると、サーバーまたはプログラムの価格更新によって上書きできなくなります。プログラムまたは価格サーバーの価格更新を希望する場合はロックしないでください", "completionRatio": "完了率", "delMultipleInfoTip": "選択した{{count}}つの価格を削除しますか？", "extraRatios": "追加拡張率", "noAvailableRatios": "利用可能な拡張倍率はありません", "noExtraRatios": "追加の拡張倍率はありません。ドロップダウンメニューから選択してください。", "promptRatio": "倍率係数", "selectExtraRatio": "拡大率を選択します"}, "profile": "プロフィール", "profilePage": {"accountBinding": "アカウントのバインディング", "bindEmail": "メールアドレスのバインド", "bindGitHubAccount": "GitHubアカウントのバインド", "bindLarkAccount": "Larkアカウントのバインド", "bindWechatAccount": "WeChatアカウントのバインド", "changeEmail": "メールアドレスの変更", "displayName": "表示名", "generateToken": "アクセストークンの生成", "inputDisplayNamePlaceholder": "表示名を入力してください", "inputPasswordPlaceholder": "パスワードを入力してください", "inputUsernamePlaceholder": "ユーザー名を入力してください", "keepSafe": "安全に保管してください。漏洩した場合はすぐにリセットしてください。", "lark": "Lark", "notBound": "未バインド", "other": "その他", "password": "パスワード", "passwordMinLength": "パスワードは8文字以上でなければなりません", "personalInfo": "個人情報", "resetToken": "アクセストークンのリセット", "submit": "送信", "telegramBot": "電報ボット", "telegramStep1": "1. 下のボタンをクリックすると、ロボットが Telegram で開きます。/start をクリックして開始します。", "telegramStep2": "2. /bind コマンドをロボットに送信した後、以下のアクセス トークンを入力してバインドします。 \n(生成されていない場合は、下のボタンをクリックして生成してください)", "token": "トークン", "tokenNotice": "注意：ここで生成されるトークンはシステム管理用であり、OpenAIサービスへのアクセスには使用できません。", "updateSuccess": "ユーザー情報が正常に更新されました！", "username": "ユーザー名", "usernameMinLength": "ユーザー名は3文字以上でなければなりません", "usernameRequired": "ユーザー名は空にできません", "wechatBindSuccess": "WeChatアカウントのバインドに成功しました！", "yourTokenIs": "あなたのアクセストークンは次の通りです："}, "redemption": "引き換え", "redemptionPage": {"createRedemptionCode": "交換コードを作成", "del": "引き換えコードを削除する", "delTip": "引き換えコードを削除するかどうか", "headLabels": {"action": "操作", "createdTime": "作成時間", "id": "ID", "name": "名前", "quota": "クォータ", "redeemedTime": "交換時間", "status": "状態"}, "pageTitle": "交換管理", "refreshButton": "更新", "searchPlaceholder": "交換コードを検索...", "successMessage": "操作が成功しました", "unredeemed": "まだ引き換えられていません"}, "redemption_edit": {"addOk": "引き換えコードが正常に作成されました。", "editOk": "引き換えコードが正常に更新されました。", "number": "量", "requiredCount": "1 以上である必要があります", "requiredQuota": "0以上である必要があります"}, "registerForm": {"confirmPasswordRequired": "確認パスワードは必須項目です", "emailRequired": "メールアドレスは必須項目です", "enterEmail": "メールアドレスを入力してください", "getCode": "確認コードを取得", "passwordRequired": "パスワードは必須項目です", "passwordsNotMatch": "パスワードが一致しません", "resendCode": "再送信({{countdown}})", "restSendEmail": "リセットメールは正常に送信されました。メールを確認してください。", "turnstileError": "数秒後に再試行してください。Turnstile がユーザー環境をチェックしています！", "usernameRequired": "ユーザー名は必須項目です", "validEmailRequired": "有効なメールアドレスである必要があります", "verificationCodeRequired": "確認コードは必須項目です", "verificationInfo": "数秒後に再試行してください。Turnstile がユーザー環境をチェックしています！"}, "registerPage": {"alreadyHaveAccount": "既にアカウントをお持ちですか？ログインをクリック"}, "res_time": {"lastTime": "前回のスピードテスト時間:", "noTest": "未検証", "second": "2番", "testClick": "クリック速度テスト（チャットモデルのみサポート）"}, "setting": "設定", "setting_index": {"operationSettings": {"chatLinkSettings": {"info": "チャットリンクを構成します。この設定は、トークン内のチャットやホームページのPlaygroundのチャットで有効です。 <br />リンクでは、&#123;key&#125;がユーザーのトークンを、&#123;server&#125;がサーバーアドレスを置き換えることができます。例えば：{'https://chat.oneapi.pro/#/?settings={\"key\":\"sk-{key}\",\"url\":\"{server}\"}'}<br />構成されていない場合、次の4つのリンクがデフォルトで構成されます：<br />ChatGPT Next ： {'https://chat.oneapi.pro/#/?settings={\"key\":\"{key}\",\"url\":\"{server}\"}'}<br />chatgpt-web-midjourney-proxy ： {'https://vercel.ddaiai.com/#/?settings={\"key\":\"{key}\",\"url\":\"{server}\"}'}<br />AMA ： {'ama://set-api-key?server={server}&key={key}'}<br />opencat ： {'opencat://team/join?domain={server}&token={key}'}<br />並べ替えルール：値が大きいほど順位が高く、同じ値の場合は構成順に並べられます", "save": "チャットリンク設定を保存", "title": "チャットリンク設定"}, "generalSettings": {"approximateToken": "計算量を減らすためにトークン数を概算する方法を使用", "chatLink": {"label": "チャットリンク", "placeholder": "例えば、ChatGPT Next Web のデプロイ先アドレス"}, "displayInCurrency": "通貨形式でクオータを表示", "displayTokenStat": "Billing 関連 API でユーザークオータではなくトークンクオータを表示", "quotaPerUnit": {"label": "単位ごとのクオータ", "placeholder": "1単位の通貨で交換できるクオータ"}, "retryCooldownSeconds": {"label": "リトライ間隔（秒）", "placeholder": "リトライ間隔（秒）"}, "retryTimes": {"label": "リトライ回数", "placeholder": "リトライ回数"}, "saveButton": "一般設定を保存", "title": "一般設定", "topUpLink": {"label": "チャージリンク", "placeholder": "例えば、カード発行ウェブサイトの購入リンク"}, "retryTimeOut": {"label": "再試行のタイムアウト時間（秒）", "placeholder": "再試行のタイムアウト時間（秒）"}, "emptyResponseBilling": "空応答課金"}, "logSettings": {"clearLogs": "履歴ログをクリア", "logCleanupTime": {"label": "ログクリーニング時間", "placeholder": "ログクリーニング時間"}, "logConsume": "ログ消費を有効にする", "title": "ログ設定"}, "monitoringSettings": {"automaticDisableChannel": "失敗時にチャネルを自動無効化", "automaticEnableChannel": "成功時にチャネルを自動有効化", "channelDisableThreshold": {"label": "最大応答時間", "placeholder": "秒単位、全ての実行チャネルがこの時間を超えると、チャネルは自動的に無効になります"}, "quotaRemindThreshold": {"label": "クォータ通知しきい値", "placeholder": "このクォータを下回ると、ユーザーに通知メールが送信されます"}, "saveMonitoringSettings": "モニタリング設定を保存", "title": "モニタリング設定"}, "otherSettings": {"CFWorkerImageUrl": {"alert": "Cloudflare Workerのイメージプロキシアドレスはこちら https://github.com/MartialBE/get-image-by-cf とイメージ検出プロキシのどちらか一方のみを設定することで利用できます。\n一部のイメージ リンクは CF アクセスを拒否し、検出エラーを引き起こす可能性があることに注意してください。", "key": "Cloudflare Worker イメージ プロキシ キー、設定されていない場合は無視してください", "label": "Cloudflareワーカーイメージプロキシ"}, "alert": "ユーザーがvisionモデルを使用して画像リンクを提供した場合、サーバーはこれらの画像をダウンロードしてトークンを計算する必要があります。画像をダウンロードする際にサーバーのIPアドレスが漏洩しないように、以下にプロキシを設定できます。このプロキシ設定はHTTPまたはSOCKS5プロキシを使用します。個人ユーザーの場合、この設定は無視できます。プロキシ形式はhttp://127.0.0.1:1080またはsocks5://127.0.0.1:1080です", "chatImageRequestProxy": {"label": "画像検出プロキシ", "placeholder": "チャット画像検出プロキシ設定、設定しないとサーバーのIPが漏洩する可能性があります"}, "mjNotify": "Midjourneyはコールバックを許可します（サーバーのIPアドレスが漏洩する可能性があります）", "saveButton": "その他の設定を保存", "title": "その他の設定", "claudeAPIEnabled": "Claude APIを有効にしますか？", "geminiAPIEnabled": "Gemini APIを有効にしますか？"}, "paymentSettings": {"alert": "支払い設定： <br />1. USD為替レート：リチャージ金額のUSD金額を計算するために使用されます <br />2. 最低リチャージ金額（USD）：最低リチャージ金額、単位はUSD、整数を入力してください <br />3. ページはすべてUSD単位で計算され、ユーザーが支払う実際の通貨は支払いゲートウェイに設定された通貨に応じて変換されます <br />例：Aゲートウェイが通貨をCNYに設定すると、ユーザーは100USDを支払い、実際の支払金額は100 * USD為替レートになります <br />Bゲートウェイが通貨をUSDに設定すると、ユーザーは100USDを支払い、実際の支払金額は100USDになります", "discount": {"label": "固定金額リチャージ割引", "placeholder": "JSONテキストで、キーはリチャージ金額、値は割引です"}, "discountInfo": "固定金額リチャージ割引の例： <br />JSONテキストで、キーはリチャージ金額、値は割引です。例えば、&#123;&quot;10&quot;:0.9&#125;は、10USDのリチャージが10％割引で計算されることを意味します <br />計算式：実際の費用＝（元の価値*割引+元の価値*割引*手数料率）*為替レート", "minAmount": {"label": "最低リチャージ金額（USD）", "placeholder": "例：1、最低リチャージ金額は1USDです、整数を入力してください"}, "save": "支払い設定を保存", "title": "支払い設定", "usdRate": {"label": "USD為替レート", "placeholder": "例：7.3"}}, "quotaSettings": {"preConsumedQuota": {"label": "リクエストの前消費クォータ", "placeholder": "リクエスト終了後に調整可能"}, "quotaForInvitee": {"label": "招待された新規ユーザーへの報酬クォータ", "placeholder": "例：1000"}, "quotaForInviter": {"label": "招待者への報酬クォータ", "placeholder": "例：2000"}, "rechargeRewardType": {"label": "チャージ返利タイプ", "fixed": "固定", "percentage": "パーセンテージ"}, "rechargeRewardValue": {"label": "チャージ返利値", "fixedPlaceholder": "例：2000", "percentagePlaceholder": "例：10"}, "quotaForNewUser": {"label": "新規ユーザーの初期クォータ", "placeholder": "例：100"}, "saveQuotaSettings": "クォータ設定を保存", "title": "クォータ設定"}, "rateSettings": {"groupRatio": {"label": "グループレート", "placeholder": "JSONテキストで、キーはグループ名、値はレートです"}, "save": "レート設定を保存", "title": "レート設定"}, "title": "運営設定", "extraTokenPriceJson": {"info": "追加トークン価格を設定します。JSON形式で、キーはモデル名であり、値は入力および出力の追加トークン価格です。例：{\"gpt-4o-audio-preview\":{\"input_audio_tokens_ratio\":40,\"output_audio_tokens_ratio\":20},\"gpt-4o-mini-audio-preview\":{\"input_audio_tokens_ratio\":67,\"output_audio_tokens_ratio\":34}}", "save": "追加トークン価格設定を保存します", "title": "追加トークン価格設定"}, "disableChannelKeywordsSettings": {"info": "キーワードを1行ずつ入力して、禁止ワードリストを設定してください。", "save": "無効なチャネルキーワード設定を保存します", "title": "チャネルキーワード設定を無効にする"}, "safetySettings": {"title": "システムセキュリティ設定", "enableSafe": "プロンプトのセキュリティチェックを有効にする", "safeToolName": {"label": "安全チェックツール"}, "safeKeyWords": {"label": "キーワードリスト", "placeholder": "1行ずつ入力してください"}, "save": "設定を保存する"}, "claudeSettings": {"budgetTokensPercentage": {"label": "デフォルトの考慮トークンパーセンテージ", "placeholder": "デフォルトの考慮トークンパーセンテージを入力してください"}, "defaultMaxTokens": {"label": "MaxTokenのデフォルト数量", "placeholder": "デフォルトのMaxToken数を入力してください。JSON形式で、defaultはデフォルト値を表します。例：{\"default\": 1000, \"claude-3-7-sonnet-latest\": 128000}"}, "save": "クロードの設定を保存します", "title": "クロードの設定"}, "geminiSettings": {"title": "Gemini設定", "geminiOpenThink": {"label": "推論出力を有効にするモデル", "placeholder": "推論出力を有効にするモデルをJSON形式で入力してください。例：{\"gemini-2.5-pro-preview-05-06\": true}"}, "save": "Gemini設定を保存"}}, "otherSettings": {"customSettings": {"aboutLabel": "概要", "aboutPlaceholder": "ここに新しい概要内容を入力してください。MarkdownおよびHTMLコードがサポートされています。リンクを入力した場合、そのリンクはiframeのsrc属性として使用され、任意のウェブページを概要ページとして設定できます。", "copyrightWarning": "One Hubの著作権表示を削除するには、まず認可を受ける必要があります。プロジェクトの維持には多大な努力が必要です。このプロジェクトがあなたにとって意味がある場合は、プロジェクトを積極的にサポートしてください。", "footerLabel": "フッター設定", "footerPlaceholder": "ここに新しいフッターを入力してください。空白のままにするとデフォルトのフッターが使用されます。HTMLコードがサポートされています。", "homePageContentLabel": "ホームページの内容", "homePageContentPlaceholder": "ここにホームページの内容を入力してください。MarkdownおよびHTMLコードがサポートされています。設定すると、ホームページのステータス情報は表示されなくなります。リンクを入力した場合、そのリンクはiframeのsrc属性として使用され、任意のウェブページをホームページとして設定できます。", "logoLabel": "ロゴ画像のURL", "logoPlaceholder": "ここにロゴ画像のURLを入力してください", "saveAbout": "概要を保存する", "saveHomePageContent": "ホームページの内容を保存する", "setFooter": "フッターを設定する", "setLogo": "ロゴを設定する", "setSystemName": "システム名を設定する", "systemNameLabel": "システム名", "systemNamePlaceholder": "ここにシステム名を入力してください", "title": "パーソナライズ設定"}, "generalSettings": {"checkUpdate": "更新を確認する", "currentVersion": "現在のバージョン", "noticeLabel": "通知", "noticePlaceholder": "ここに新しい通知内容を入力してください。MarkdownおよびHTMLコードがサポートされています。", "saveNotice": "通知を保存する", "title": "一般設定"}, "title": "その他の設定", "updateDialog": {"close": "閉じる", "newVersion": "新しいバージョン", "viewGitHub": "GitHubで表示する"}}, "systemSettings": {"configureEmailDomainWhitelist": {"allowedEmailDomains": "許可されたメールドメイン", "emailDomainRestriction": "メールドメインホワイトリストを有効にする", "save": "メールドメインホワイトリスト設定を保存", "subTitle": "一時的なメールアドレスを使用した悪意のあるユーザー登録を防ぐため", "title": "メールドメインホワイトリストの設定"}, "configureFeishuAuthorization": {"alert1": "ホームページ URL を入力してください", "alert2": "、リダイレクト URL を入力してください", "appId": "App ID", "appIdPlaceholder": "App ID を入力してください", "appSecret": "App Secret", "appSecretPlaceholder": "敏感情報はフロントエンドに送信されません", "manage": "飞书アプリを管理する", "manageLink": "こちらをクリックして", "saveButton": "飞书 OAuth 設定を保存する", "subTitle": "飞书を利用したログインおよび登録をサポートするための設定です。", "title": "飞书授权の設定"}, "configureGitHubOAuthApp": {"alert1": "Homepage URL を入力してください", "alert2": "、Authorization callback URL を入力してください", "clientId": "GitHub クライアント ID", "clientIdPlaceholder": "登録した GitHub OAuth アプリの ID を入力してください", "clientSecret": "GitHub クライアントシークレット", "clientSecretPlaceholder": "敏感情報はフロントエンドに送信されません", "manage": "GitHub OAuth アプリを管理する", "manageLink": "こちらをクリックして", "saveButton": "GitHub OAuth 設定を保存する", "subTitle": "GitHub を利用したログインおよび登録をサポートするための設定です。", "title": "GitHub OAuth アプリの設定"}, "configureLoginRegister": {"emailVerification": "パスワード登録時にメール認証が必要", "gitHubOAuth": "GitHubアカウントでのログイン＆登録を許可", "gitHubOldIdClose": "GitHub の古い ID ログインを閉じる", "larkAuth": "Larkでのログイン＆登録を許可", "oidcAuth": "OIDCアカウントを使用してログイン＆登録を許可", "passwordLogin": "パスワードでログインを許可", "passwordRegister": "パスワードで登録を許可", "registerEnabled": "新規ユーザー登録を許可（これを無効にすると、新規登録はできません）", "title": "ログインと登録の設定", "turnstileCheck": "Turnstileユーザー検証を有効にする", "weChatAuth": "WeChatでのログイン＆登録を許可"}, "configureOIDCAuthorization": {"alert1": "ホームページリンクを入力してください", "alert2": "，リダイレクトURLを入力してください", "clientId": "クライアントID（Client ID）", "clientIdPlaceholder": "クライアントIDを入力してください", "clientSecret": "クライアントシークレット", "clientSecretPlaceholder": "機密情報はフロントエンドに送信されません", "issuer": "OIDC発行者（Issuer）", "issuerPlaceholder": "OIDC発行者を入力してください", "saveButton": "OIDC設定を保存", "scopes": "スコープ", "scopesPlaceholder": "スコープを入力してください（英文カンマで区切る） 通常は 'openid,email,profile'", "subTitle": "標準的なOIDC認可ログインシステムを設定するためのもの", "title": "OIDC統合認可システムの設定", "usernameClaims": "ユーザー名のクレーム", "usernameClaimsPlaceholder": "ユーザー名のクレームを入力してください（例：username）"}, "configureSMTP": {"alert": "一部のメールプロバイダーは送信メールにサーバーIPアドレスを含めます。非個人使用の場合は、プロフェッショナルなメールサービスプロバイダーの使用を検討してください。", "save": "SMTP設定を保存", "smtpAccount": "SMTPアカウント", "smtpAccountPlaceholder": "通常はメールアドレス", "smtpFrom": "SMTP送信者メール", "smtpFromPlaceholder": "通常はメールアドレスと一致", "smtpPort": "SMTPポート", "smtpPortPlaceholder": "デフォルト：587", "smtpServer": "SMTPサーバーアドレス", "smtpServerPlaceholder": "例：smtp.qq.com", "smtpToken": "SMTPアクセス認証", "smtpTokenPlaceholder": "機密情報はフロントエンドに表示されません", "subTitle": "システムのメール送信をサポート", "title": "SMTP設定"}, "configureTurnstile": {"manage": "Turnstile サイトを管理する。Invisible Widget Type を推奨します。", "manageLink": "こちらをクリックして", "saveButton": "Turnstile 設定を保存する", "secretKey": "Turnstile シークレットキー", "secretKeyPlaceholder": "敏感情報はフロントエンドに送信されません", "siteKey": "Turnstile サイトキー", "siteKeyPlaceholder": "登録した Turnstile サイトキーを入力してください", "subTitle": "ユーザー認証をサポートするための設定です。", "title": "Turnstile の設定"}, "configureWeChatServer": {"accessToken": "WeChat サーバーアクセス トークン", "accessTokenPlaceholder": "敏感情報はフロントエンドに送信されません", "learn": "WeChat サーバーについて詳しく学ぶ", "learnLink": "こちらをクリックして", "qrCodeImage": "WeChat 公式アカウント QR コード画像リンク", "qrCodeImagePlaceholder": "画像リンクを入力してください", "saveButton": "WeChat サーバーの設定を保存する", "serverAddress": "WeChat サーバーのアドレス", "serverAddressPlaceholder": "例：https://yourdomain.com", "subTitle": "WeChat を利用したログインおよび登録をサポートするための設定です。", "title": "WeChat サーバーの設定"}, "generalSettings": {"serverAddress": "サーバーアドレス", "serverAddressPlaceholder": "例：https://yourdomain.com", "title": "システム設定", "updateServerAddress": "サーバーアドレスを更新"}, "title": "システム設定"}}, "shimmer 映射": "シマーマッピング", "suno": {"lyrics": "歌詞", "music": "オーディオ", "response": "レスポンスボディ", "video": "ビデオ"}, "tableToolBar": {"channelId": "チャネルID", "channelIdPlaceholder": "チャネルID", "endTime": "終了時間", "modelName": "モデル名", "startTime": "開始時間", "taskId": "タスクID", "taskIdPlaceholder": "タスクID", "tokenName": "トークン名", "type": "タイプ", "username": "ユーザー名", "sourceIp": "Source IP"}, "task": "非同期タスク", "taskPage": {"channel": "チャネル", "fail": "失敗の理由", "finishTime": "完了時間", "platform": "プラットホーム", "progress": "スケジュール", "status": "タスクのステータス (クリックして結果を表示)", "subTime": "提出時間", "task": "タスクID", "time": "時間がかかる", "type": "タイプ", "user": "ユーザー", "title": "非同期タスク"}, "telegramPage": {"action": "アクション", "command": "コマンド", "createMenu": "作成", "description": "説明", "id": "ID", "infoMessage": "メニューコマンド/説明を追加または変更した後（コマンドと説明を変更していない場合はリロードする必要はありません）、メニューをリロードして有効にする必要があります。新しいメニューが表示されない場合は、バックエンドを停止してプログラムを再起動してください。", "offline": "オフライン(Polling)", "online": "オンライン(Webhook)", "operationSuccess": "操作が正常に完了しました！", "refresh": "リフレッシュ", "reloadMenu": "メニューをリロード", "reloadSuccess": "リロードが成功しました！", "replyContent": "返信内容", "replyType": "返信タイプ", "searchPlaceholder": "IDとコマンドを検索...", "title": "Telegramボットメニュー"}, "telegram_edit": {"addOk": "メニューが正常に作成されました。", "msgInfo": "メッセージ内容", "msgType": "メッセージの種類", "requiredCommand": "コマンドを空にすることはできません", "requiredDes": "説明を空にすることはできません", "requiredMes": "メッセージの内容を空にすることはできません", "requiredParseMode": "メッセージタイプを空にすることはできません", "updateOk": "メニューが正常に更新されました!"}, "token": "トークン", "token_index": {"actions": "操作", "cancel": "キャンセル", "chat": "チャット", "close": "閉じる", "confirmDeleteToken": "トークンを削除しますか", "copy": "コピー", "createToken": "トークンを作成する", "createdTime": "作成日時", "delete": "削除", "deleteToken": "トークンを削除", "editToken": "トークンを編集", "enableCache": "キャッシュを有効にする（有効にすると、チャット履歴をキャッシュして消費を減らす）", "expiryTime": "有効期限", "invalidDate": "無効な日付", "name": "名前", "neverExpires": "期限なし", "quota": "クォータ", "quotaNote": "注意：トークンのクォータは、トークン自体の最大使用量を制限するためのものであり、実際の使用はアカウントの残りクォータによって制限されます。", "refresh": "リフレッシュ", "remainingQuota": "残りクォータ", "replaceApiAddress1": "OpenAI APIの基本アドレスhttps://api.openai.comを", "replaceApiAddress2": "に置き換え、以下のキーをコピーして使用してください。", "searchTokenName": "トークン名を検索...", "status": "ステータス", "submit": "提出する", "token": "トークン", "unlimited": "制限なし", "unlimitedQuota": "無制限のクォータ", "usedQuota": "使用済みクォータ", "userGroup": "グループ", "apiRate": "", "apiRateTip": "", "heartbeat": "心拍設定（実験的）", "heartbeatTip": "心拍設定とは、リクエスト時に長時間データが返ってこない場合、クライアントがタイムアウト機構によって接続を切断する可能性があることを指します。TCP接続がタイムアウトによって中断されないようにするため、心拍設定を有効にすることができます。設定した開始時間を超えて応答がない場合、5秒ごとにハートビートリクエスト（ストリームでないリクエストは空行、ストリームの場合は::PING）を送信し、接続を維持します。ご注意：中継プログラムを使用している場合は、この設定を有効にしないでください。予期しない問題が発生する可能性があります。", "heartbeatTimeout": "ハートビート開始時間(単位：秒)", "heartbeatTimeoutHelperText": "最小値は30秒、最大値は90秒です"}, "topup": "トップアップ", "topupCard": {"actualAmountToPay": "実際の支払金額", "adminSetupRequired": "管理者がチャージリンクを設定していません！", "amount": "チャージ金額", "amountMaxLimit": "金額は1000000を超えることはできません", "amountMinLimit": "金額は少なくとも", "currentQuota": "現在の割り当て", "discountedPrice": "割引後の価格", "exchangeButton": {"default": "交換", "submitting": "送信中"}, "exchangeRate": "為替レート", "fee": "手数料", "getRedemptionCode": "引き換えコードを取得", "inputLabel": "引き換えコード", "inputPlaceholder": "引き換えコードを入力してください！", "noRedemptionCodeText": "まだ引き換えコードがありませんか？ コードを取得するにはクリックしてください：", "onlineTopup": "オンラインチャージ", "positiveIntegerAmount": "正の整数金額を入力してください", "redemptionCodeTopup": "引き換えコードチャージ", "selectPaymentMethod": "支払い方法を選択してください", "topup": "チャージ", "topupAmount": "チャージ金額", "topupsuccess": "チャージが成功しました！"}, "topupPage": {"alertMessage": "チャージ記録および招待記録はログで確認してください。チャージ記録はログでタイプ【チャージ】を選択して確認してください；招待記録はログで【システム】を選択して確認してください"}, "ui-component": {"allModels": "すべてのモデルを見る", "modelName": "機種名"}, "user": "ユーザー", "userGroup": {"apiRate": "APIレート", "apiRateTip": "1分あたりのリクエスト数は、速度が60未満の場合はカウンターリミッターを使用し、速度が60以上の場合はトークンバケットリミッターを使用します。Redisが有効な場合にのみ適用されます。", "create": "新しいグループを作成", "enable": "有効にします", "id": "ID\n\nID", "name": "名前", "nameTip": "ユーザーに表示する名前", "public": "公開されていますか？", "promotion": "自動アップグレード", "promotionTip": "有効にすると、ユーザーのチャージ金額が最小-最大条件を満たした場合、自動的にこのユーザーグループにアップグレードされます", "min": "最小金額", "minTip": "自動アップグレードに必要な最小チャージ金額", "max": "最大金額", "maxTip": "このユーザーグループレベルの最大チャージ金額", "ratio": "倍率 -> 倍率", "symbol": "識別", "symbolTip": "ユーザーグループを区別するための識別子を使用してください。英語で入力し、重複しないようにしてください。", "title": "ユーザーグループ"}, "userPage": {"action": "アクション", "adminUserRole": "管理者", "bind": "バインド", "cUserRole": "一般ユーザー", "cancel": "キャンセル", "cancelAdmin": "管理者のキャンセル", "createUser": "ユーザーを作成", "creationTime": "作成時間", "del": "ユーザーを削除する", "delTip": "ユーザーを削除するかどうか", "displayName": "表示名", "editUser": "ユーザーを編集", "group": "グループ", "groupRequired": "グループは必須です", "id": "ID", "operationSuccess": "操作が正常に完了しました！", "password": "パスワード", "passwordRequired": "パスワードは必須です", "quota": "クォータ", "quotaMin": "クォータは0未満にすることはできません", "refresh": "リフレッシュ", "saveSuccess": "保存しました！", "searchPlaceholder": "ユーザーのID、ユーザー名、グループ、表示名、またはメールアドレスを検索...", "setAdmin": "管理者として設定する", "statistics": "統計情報", "status": "状態", "submit": "送信", "superAdminRole": "スーパー管理者", "uUserRole": "不明な身元", "useQuota": "リクエスト数", "userRole": "ユーザーの役割", "username": "ユーザー名", "usernameRequired": "ユーザー名は必須です", "users": "ユーザー", "changeQuota": "増減枠", "changeQuotaHelperText": "こちらは増減です。ユーザーの残高を直接変更するものではありません。ドルを入力し、最大で {{quota}} を差し引くことができます。", "changeQuotaNotEmpty": "変更枠を記入してください", "changeQuotaNotEnough": "ユーザーの残高を超える金額を差し引くことはできません。", "quotaRemark": "コメント"}, "user_group": "ユーザーグループ", "validation": {"requiredName": "名前は必須です"}, "仅支持聊天": "チャットのみ", "从Cohere获取模型列表": "Cohere からモデルのリストを取得する", "从Deepseek获取模型列表": "Deepseekからモデルリストを取得", "从Gemini获取模型列表": "Geminiからモデルリストを取得", "从Groq获取模型列表": "Groq からモデルのリストを取得する", "从Mistral获取模型列表": "ミストラルからモデルリストを取得", "从Moonshot获取模型列表": "Moonshotからモデルリストを取得", "从OpenAI获取模型列表": "OpenAIからモデルリストを取得", "从渠道获取模型列表": "チャンネルからモデルリストを取得", "代理地址": "プロキシアドレス", "代码执行": "コードの実行", "位置/区域": "場所・エリア", "你可以为你的渠道打一个标签，打完标签后，可以通过标签进行批量管理渠道，注意：设置标签后某些设置只能通过渠道标签修改，无法在渠道列表中修改。": "あなたはチャネルにタグを付けることができます。タグをつけた後、タグを使ってチャネルを一括管理できます。 注意：タグを設定した後、一部の設定はチャネルのリストでは変更できず、チャネルのタグでのみ変更できます。", "使用代码执行功能，开启后，计算tokens不准确，建议个人使用开启": "コード実行機能を使用する場合、オンにするとトークンの計算が不正確になるため、個人で使用することを推奨します。", "使用网页搜索功能，对用户输入的内容进行搜索": "Web検索機能を使用して、ユーザーが入力したコンテンツを検索します。", "其他参数": "その他のパラメータ", "可空，请输入中转API地址，例如通过cloudflare中转": "空のままにすることもできます。たとえば、cloudflare 転送を通じて転送 API アドレスを入力してください。{model}変数をサポートしています、例：https://api.example.com/v1/{model}/chat", "启用": "有効にする", "地址填写Suno-API部署的地址": "アドレス: Suno-API デプロイメントのアドレスを入力します。", "地址填写midjourney-proxy部署的地址": "アドレス: Midjourney-Proxy デプロイメントのアドレスを入力します。", "声音映射": "サウンドマッピング", "如果选择了仅支持聊天，那么遇到有函数调用的请求会跳过该渠道": "チャットのみをサポートすることを選択した場合、関数呼び出しを伴うリクエストに遭遇したときにチャネルはスキップされます。", "密钥": "鍵", "密钥填写Suno-API的密钥，如果没有设置密钥，可以随便填": "キーにはSuno-APIのキーを記入します。キーが設定されていない場合は気軽に記入してください。", "密钥填写midjourney-proxy的密钥，如果没有设置密钥，可以随便填": "キーはmidjourney-proxyのキーです。キーが設定されていない場合は気軽に入力してください。", "将OpenAI的声音角色映射到azure的声音角色, 如果有role，请用|隔开，例如zh-CN-YunxiNeural|boy": "OpenAI の音声ロールを Azure の音声ロールにマップします。ロールがある場合は、zh-CN-YunxiNeural|boy のように | で区切ってください。", "当涉及到知识库ID时，请前往开放平台的知识库模块进行创建或获取(是知识库ID不是文档ID！)": "ナレッジ ベース ID については、オープン プラットフォームのナレッジ ベース モジュールに移動して作成または取得してください (これはドキュメント ID ではなく、ナレッジ ベース ID です)。", "必须填写所有数据后才能获取模型列表": "モデルリストを取得するには、すべてのデータを入力する必要があります", "按照如下格式输入：APIKey-AppId，例如：fastgpt-0sp2gtvfdgyi4k30jwlgwf1i-64f335d84283f05518e9e041": "APIKey-AppId の形式で入力します。例: fastgpt-0sp2gtvfdgyi4k30jwlgwf1i-64f335d84283f05518e9e041", "按照如下格式输入：APIKey|SecretKey": "次の形式に従って入力します: APIKey|SecretKey", "按照如下格式输入：APISecret|groupID": "次の形式で入力します: APISecret|groupID", "按照如下格式输入：APPID|APISecret|APIKey": "次の形式で入力します: APPID|APISecret|APIKey", "按照如下格式输入：AppId|SecretId|SecretKey": "次の形式で入力します: AppId|SecretId|SecretKey", "按照如下格式输入：CLOUDFLARE_ACCOUNT_ID|CLOUDFLARE_API_TOKEN": "次の形式で入力します: CLOUDFLARE_ACCOUNT_ID|CLOUDFLARE_API_TOKEN", "按照如下格式输入：Region|AccessKeyID|SecretAccessKey|SessionToken 其中SessionToken可不填空": "次の形式で入力します:Region|AccessKeyID|SecretAccessKey|SessionToken。SessionToken は空白のままにすることができます。", "按照如下格式输入：SecretId|SecretKey": "次の形式で入力します: SecretId|SecretKey", "插件参数": "プラグインパラメータ", "是否启用代码执行": "コードの実行を有効にするかどうか", "是否启用网页搜索": "Web検索を有効にするかどうか", "替换 API 版本": "API バージョンを置き換える", "本配置主要是用于使用cloudflare Zero Trust将端口暴露到公网时，需要配置的header": "この設定は主に、cloudflare ゼロ トラストを使用してポートをパブリック ネットワークに公開するときに設定する必要があるヘッダーに使用されます。", "标签": "ラベル", "模型": "モデル", "模型名称为coze-{bot_id}，你也可以直接使用 coze-* 通配符来匹配所有coze开头的模型": "モデル名は coze-{bot_id} です。coze-* ワイルドカードを直接使用して、coze で始まるすべてのモデルと一致させることもできます。", "模型名称映射， 你可以取一个容易记忆的名字来代替coze-{bot_id}，例如：{\"coze-translate\": \"coze-xxxxx\"},注意：如果使用了模型映射，那么上面的模型名称必须使用映射前的名称，上述例子中，你应该在模型中填入coze-translate(如果已经使用了coze-*，可以忽略)。": "モデル名のマッピングでは、coze-{bot_id} を覚えやすい名前に置き換えることができます (例: {\"coze-translate\": \"coze-xxxxx\"})。 注: モデル マッピングが使用されている場合は、上記のモデル名が使用されます。上記の例では、モデルに coze-translate を入力する必要があります (coze-* がすでに使用されている場合は無視できます)。", "模型映射关系": "モデルマッピング関係", "测速模型": "スピードモデル", "渠道API地址": "チャネルAPIアドレス", "渠道名称": "チャンネル名", "渠道类型": "チャネルの種類", "版本号": "バージョンナンバー", "用于测试使用的模型，为空时无法测速,如：gpt-3.5-turbo，仅支持chat模型": "テストに使用したモデルは空の場合は速度を測定できません。例: gpt-3.5-turbo はチャット モデルのみをサポートします。", "用户组": "ユーザー・グループ", "知识库": "知識ベース", "知识库ID": "ナレッジベースID", "知识库模板": "ナレッジベースのテンプレート", "网页搜索": "ウェブ検索", "请为渠道命名": "チャンネル名を付けてください", "请前往开放平台的知识库上传文档，然后使用知识库功能进行检索。": "オープンプラットフォームのナレッジベースにアクセスしてドキュメントをアップロードし、ナレッジベース機能を使用して検索してください。", "请参考wiki中的文档获取key. https://github.com/MartialBE/one-hub/wiki/VertexAI": "キーを取得するには、Wiki のドキュメントを参照してください。 https://github.com/MartialBE/one-hub/wiki/VertexAI", "请填写AZURE_OPENAI_ENDPOINT": "AZURE_OPENAI_ENDPOINTを入力してください", "请求模型时的知识库模板, 请查看文档填写，否则不用填写": "ナレッジベーステンプレートは、モデルをリクエストする際にドキュメントを確認して記入してください。それ以外の場合は記入する必要はありません", "请输入你 Speech Studio 的位置/区域，例如：eastasia": "Speech Studio の場所/地域を入力してください (例: eastasia)", "请输入你部署的Ollama地址，例如：http://127.0.0.1:11434，如果你使用了cloudflare Zero Trust，可以在下方插件填入授权信息": "デプロイした Ollama アドレスを入力してください (例: http://127.0.0.1:11434)。cloudflare ゼロ トラストを使用する場合は、以下のプラグインに認証情報を入力できます。", "请输入插件参数，即 X-DashScope-Plugin 请求头的取值": "プラグイン パラメーター、つまり X-DashScope-Plugin リクエスト ヘッダーの値を入力してください。", "请输入渠道对应的鉴权密钥": "チャンネルに対応する認証キーを入力してください", "请输入版本号，例如：v1": "バージョン番号を入力してください (例: v1)", "请输入版本号，例如：v3.1": "バージョン番号を入力してください、例：v3.1", "请输入默认API版本，例如：2024-05-01-preview": "デフォルトの API バージョンを入力してください (例: 2024-05-01-preview)", "请选择渠道类型": "チャンネルタイプを選択してください", "请选择该渠道所支持的模型,你也可以输入通配符*来匹配模型，例如：gpt-3.5*，表示支持所有gpt-3.5开头的模型，*号只能在最后一位使用，前面必须有字符，例如：gpt-3.5*是正确的，*gpt-3.5是错误的": "このチャネルでサポートされているモデルを選択してください。たとえば、gpt-3.5* のように、ワイルドカード文字 * を入力することもできます。これは、gpt-3.5 で始まるすべてのモデルが使用できることを意味します。最後の桁に があり、その前に文字が必要です。例: gpt-3.5* は正しいですが、*gpt-3.5 は間違っています。", "请选择该渠道所支持的用户组": "このチャンネルでサポートされているユーザー グループを選択してください", "请随意填写": "お気軽にご記入ください", "输入后，会替换请求地址中的v1，例如：freeapi，则请求chat时会变成https://xxx.com/freeapi/chat/completions,如果需要禁用版本号，请输入 disable": "入力後、リクエストアドレスのv1が置き換えられます。例：freeapi、チャットをリクエストする場合は、https://xxx.com/freeapi/chat/completions となります。バージョン番号を無効にする必要がある場合は、「disable」と入力してください。", "这里选择预计费选项，用于预估费用，如果你觉得计算图片占用太多资源，可以选择关闭图片计费。但是请注意：有些渠道在stream下是不会返回tokens的，这会导致输入tokens计算错误。": "こちらでは料金予測オプションを選択し、費用の見積もりに使用します。画像計算がリソースを多く消費すると感じた場合は、画像計算を無効にすることができます。ただし、注意してください：一部のチャネルはstream下ではトークンを返さないため、入力トークンが誤って計算される可能性があります。", "预计费选项": "見積オプション", "默认 API 版本": "デフォルトのAPIバージョン", "默认 zh-CN-XiaochenNeural": "デフォルトのzh-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "默认 zh-CN-XiaohanNeural": "デフォルトのzh-<PERSON><PERSON><PERSON><PERSON><PERSON>N<PERSON><PERSON>", "默认 zh-CN-YunxiNeural": "デフォルトのzh-CN-YunxiNeural", "默认 zh-CN-YunxiNeural|boy": "デフォルト zh-CN-YunxiNeural|boy", "默认 zh-CN-YunyangNeural": "デフォルトのzh-CN-YunyangNeural", "默认 zh-CN-YunyeNeural": "デフォルトのzh-CN-YunyeNeural", "plauground": "", "modelOwnedby": {"action": "操作を実行します", "create": "新しいモデルの帰属", "icon": "アイコン", "iconTip": "モデルリストでの表示にアイコンを使用します", "id": "ID", "idTip": "チャネルIDは数字で、1000以上の値を設定し、一旦設定したら変更できないようにしてください。", "name": "名前", "nameTip": "チャネル名", "title": "モデルの帰属"}, "price": "価格", "模型映射关系：例如用户请求A模型，实际转发给渠道的模型为B。在B模型加前缀+，表示使用传入模型计费，例如：+gpt-3": {"5-turbo": "モデルマッピング関係：例えば、ユーザーがAモデルをリクエストした場合、実際にチャンネルに転送されるのはBモデルです。 Bモデルにプレフィックス+を付けて、入力されたモデルを使用して課金することを示します。 例：+gpt-3.5-turbo"}, "单独设置代理地址，支持http和socks5，例如：http://127[0][0]": {"1:1080,代理地址中可以通过 `%s` 作为会话标识占位符，程序中检测到有占位符会根据Key生成唯一会话标识符进行替换": "単独のプロキシアドレスを設定し、httpおよびsocks5をサポートします。例：http://127.0.0.1:1080。プロキシアドレスには`％s`をセッション識別子のプレースホルダとして使用できます。プログラムがプレースホルダを検出すると、Keyに基づいて一意のセッション識別子を生成して置換します。"}, "请输入版本号，例如：v3": {"__i18n_ally_root__": {"1": ""}}, "请选择该渠道所支持的模型,你也可以输入通配符*来匹配模型，例如：gpt-3": {"__i18n_ally_root__": {"5*，表示支持所有gpt-3": {"5开头的模型，*号只能在最后一位使用，前面必须有字符，例如：gpt-3": {"5*是正确的，*gpt-3": {"5是错误的": ""}}}, "__i18n_ally_root__": {"5*，表示支持所有gpt-3": {"5开头的模型，*号只能在最后一位使用，前面必须有字符，例如：gpt-3": {"5*是正确的，*gpt-3": {"5是错误的": ""}}}}}}, "输入后，会替换请求地址中的v1，例如：freeapi，则请求chat时会变成https://xxx": {"__i18n_ally_root__": {"__i18n_ally_root__": {"com/freeapi/chat/completions,如果需要禁用版本号，请输入 disable": ""}, "com/freeapi/chat/completions,如果需要禁用版本号，请输入 disable": ""}}, "用于测试使用的模型，为空时无法测速,如：gpt-3": {"__i18n_ally_root__": {"5-turbo，仅支持chat模型": ""}}, "禁用流式的模型": "フロー型モデルを無効にする", "请参考wiki中的文档获取key": {"__i18n_ally_root__": {" https://github": {"com/MartialBE/one-hub/wiki/VertexAI": ""}}}, "请输入你部署的Ollama地址，例如：http://127[__i18n_ally_root__]": {"0": {"0": {"1:11434，如果你使用了cloudflare Zero Trust，可以在下方插件填入授权信息": ""}}}, "这里填写禁用流式的模型，注意：如果填写了禁用流式的模型，那么这些模型在流式请求时会跳过该渠道": "ここには、ストリーミングを無効にするモデルを記入してください。注意：ストリーミングを無効にするモデルを記入した場合、これらのモデルはストリームリクエスト時にそのチャンネルをスキップします。"}