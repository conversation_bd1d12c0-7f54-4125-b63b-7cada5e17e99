Our Golang code uses the Gin framework, and JavaScript uses vite react. Therefore, when we discuss Golang code, please provide code examples and explanations applicable to the Gin framework. When we discuss JavaScript, please provide code examples and explanations suitable for vite react.

Our program's database uses the gorm package and needs to support MySQL/PostgreSQL/SQLite simultaneously. We determine the current database in use through global variables: "common.UsingPostgreSQL" represents that PostgreSQL is currently in use, "common.UsingSQLite" represents that SQLite is in use, and if both are false, it indicates that MySQL is currently in use. So when we discuss database issues, we need code examples and explanations that are applicable to gorm and adaptable to multiple databases.

Our program uses github.com/redis/go-redis/v9 for Redis, and determines whether Redis is enabled through the global variable "common.RedisEnabled". Therefore, when we discuss caching issues, we need to check if Redis is enabled and provide the correct code examples and explanations.
