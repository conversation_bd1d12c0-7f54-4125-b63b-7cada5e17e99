package controller

import (
	"done-hub/common"
	"done-hub/common/config"
	"done-hub/common/stmp"
	"done-hub/common/telegram"
	"done-hub/model"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

func GetStatus(c *gin.Context) {
	telegramBot := ""
	if telegram.TGEnabled {
		telegramBot = telegram.TGBot.User.Username
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data": gin.H{
			"version":             config.Version,
			"start_time":          config.StartTime,
			"email_verification":  config.EmailVerificationEnabled,
			"github_oauth":        config.GitHubOAuthEnabled,
			"github_client_id":    config.GitHubClientId,
			"oidc_auth":           config.OIDCAuthEnabled,
			"lark_login":          config.LarkAuthEnabled,
			"lark_client_id":      config.LarkClientId,
			"system_name":         config.SystemName,
			"logo":                config.Logo,
			"language":            config.Language,
			"footer_html":         config.Footer,
			"wechat_qrcode":       config.WeChatAccountQRCodeImageURL,
			"wechat_login":        config.WeChatAuthEnabled,
			"server_address":      config.ServerAddress,
			"turnstile_check":     config.TurnstileCheckEnabled,
			"turnstile_site_key":  config.TurnstileSiteKey,
			"top_up_link":         config.TopUpLink,
			"chat_link":           config.ChatLink,
			"quota_per_unit":      config.QuotaPerUnit,
			"display_in_currency": config.DisplayInCurrencyEnabled,
			"telegram_bot":        telegramBot,
			"mj_notify_enabled":   config.MjNotifyEnabled,
			"chat_links":          config.ChatLinks,
			"PaymentUSDRate":      config.PaymentUSDRate,
			"PaymentMinAmount":    config.PaymentMinAmount,
			"RechargeDiscount":    config.RechargeDiscount,
			"EnableSafe":          config.EnableSafe,
			"SafeToolName":        config.SafeToolName,
			"SafeKeyWords":        config.SafeKeyWords,
			"UserInvoiceMonth":    config.UserInvoiceMonth,
			"UptimeDomain":        config.UPTIMEKUMA_DOMAIN,
			"UptimePageName":      config.UPTIMEKUMA_STATUS_PAGE_NAME,
			"UptimeEnabled":       config.UPTIMEKUMA_ENABLE,
		},
	})
}

func GetNotice(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    config.GlobalOption.Get("Notice"),
	})
}

func GetAbout(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    config.GlobalOption.Get("About"),
	})
}

func GetHomePageContent(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    config.GlobalOption.Get("HomePageContent"),
	})
}

func SendEmailVerification(c *gin.Context) {
	email := c.Query("email")
	if err := common.Validate.Var(email, "required,email"); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}
	if config.EmailDomainRestrictionEnabled {
		allowed := false
		for _, domain := range config.EmailDomainWhitelist {
			if strings.HasSuffix(email, "@"+domain) {
				allowed = true
				break
			}
		}
		if !allowed {
			c.JSON(http.StatusOK, gin.H{
				"success": false,
				"message": "管理员启用了邮箱域名白名单，您的邮箱地址的域名不在白名单中",
			})
			return
		}
	}
	if model.IsEmailAlreadyTaken(email) {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "邮箱地址已被占用",
		})
		return
	}
	code := common.GenerateVerificationCode(6)
	common.RegisterVerificationCodeWithKey(email, code, common.EmailVerificationPurpose)
	err := stmp.SendVerificationCodeEmail(email, code)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
}

func SendPasswordResetEmail(c *gin.Context) {
	email := c.Query("email")
	if err := common.Validate.Var(email, "required,email"); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}

	user := &model.User{
		Email: email,
	}

	if err := user.FillUserByEmail(); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "该邮箱地址未注册",
		})
		return
	}

	userName := user.DisplayName
	if userName == "" {
		userName = user.Username
	}

	code := common.GenerateVerificationCode(0)
	common.RegisterVerificationCodeWithKey(email, code, common.PasswordResetPurpose)
	link := fmt.Sprintf("%s/user/reset?email=%s&token=%s", config.ServerAddress, email, code)
	err := stmp.SendPasswordResetEmail(userName, email, link)

	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
	})
}

type PasswordResetRequest struct {
	Email string `json:"email"`
	Token string `json:"token"`
}

func ResetPassword(c *gin.Context) {
	var req PasswordResetRequest
	err := json.NewDecoder(c.Request.Body).Decode(&req)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}

	if req.Email == "" || req.Token == "" {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "无效的参数",
		})
		return
	}
	if !common.VerifyCodeWithKey(req.Email, req.Token, common.PasswordResetPurpose) {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "重置链接非法或已过期",
		})
		return
	}
	password := common.GenerateVerificationCode(12)
	err = model.ResetUserPasswordByEmail(req.Email, password)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	common.DeleteKey(req.Email, common.PasswordResetPurpose)
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    password,
	})
}
